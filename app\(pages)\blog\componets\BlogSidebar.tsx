"use client"
import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { BsSearch } from "react-icons/bs"
import { formatBlogDate, BlogData } from '@/api/blogs/blogs_api'

interface BlogSidebarProps {
  categories: Array<{ name: string; count: number }>
  recentBlogs: BlogData[]
  allKeywords: string[]
  searchQuery: string
  selectedCategory: string
  onSearchChange: (query: string) => void
  onSearchSubmit: (e: React.FormEvent) => void
  onCategoryFilter: (category: string) => void
  onKeywordClick: (keyword: string) => void
}

export default function BlogSidebar({
  categories,
  recentBlogs,
  allKeywords,
  searchQuery,
  selectedCategory,
  onSearchChange,
  onSearchSubmit,
  onCategoryFilter,
  onKeywordClick
}: BlogSidebarProps) {
  const router = useRouter()
  const [showAllCategories, setShowAllCategories] = useState(false)
  const [showAllKeywords, setShowAllKeywords] = useState(false)

  const handleBlogClick = (blogId: string, title: string) => {
    const slug = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
    router.push(`/blog/${blogId}/${slug}`)
  }

  return (
    <div className="w-[350px] flex flex-col gap-8">
      {/* Search Section */}
      <div className="bg-zinc-900 rounded-[20px] p-6">
        <h3 className="text-white text-[20px] font-bold mb-4">Search</h3>
        <form onSubmit={onSearchSubmit} className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            placeholder="Search blogs..."
            className="w-full bg-zinc-800 border border-zinc-700 rounded-lg px-4 py-3 pr-12 text-white placeholder-white/50 focus:outline-none focus:border-orange-500"
          />
          <button
            type="submit"
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-orange-500 transition-colors"
          >
            <BsSearch className="w-5 h-5" />
          </button>
        </form>
      </div>

      {/* Categories Section */}
      <div className="bg-zinc-900 rounded-[20px] p-6">
        <h3 className="text-white text-[20px] font-bold mb-4">Category</h3>
        <div className="space-y-3">
          {categories.slice(0, showAllCategories ? categories.length : 5).map((category, index) => (
            <div
              key={index}
              onClick={() => onCategoryFilter(category.name)}
              className={`flex justify-between items-center p-3 rounded-lg cursor-pointer transition-colors ${
                selectedCategory === category.name 
                  ? 'bg-orange-500 text-white' 
                  : 'bg-zinc-800 text-white/80 hover:bg-zinc-700'
              }`}
            >
              <span className="font-medium">{category.name}</span>
              <span className="text-sm">({category.count})</span>
            </div>
          ))}
          {categories.length > 5 && (
            <button
              onClick={() => setShowAllCategories(!showAllCategories)}
              className="w-full text-orange-500 hover:text-orange-400 text-sm font-medium py-2 transition-colors"
            >
              {showAllCategories ? 'Show Less' : 'More'}
            </button>
          )}
        </div>
      </div>

      {/* Recent News Section */}
      <div className="bg-zinc-900 rounded-[20px] p-6">
        <h3 className="text-white text-[20px] font-bold mb-4">Recent News</h3>
        <div className="space-y-4">
          {recentBlogs.map((blog) => (
            <div
              key={blog._id}
              onClick={() => handleBlogClick(blog._id, blog.title)}
              className="flex gap-3 cursor-pointer group"
            >
              <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                <Image
                  src={blog.imageUrl}
                  alt={blog.title}
                  width={64}
                  height={64}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
              </div>
              <div className="flex-1">
                <h4 className="text-white text-sm font-medium line-clamp-2 group-hover:text-orange-500 transition-colors">
                  {blog.title}
                </h4>
                <p className="text-white/50 text-xs mt-1">
                  {formatBlogDate(blog.createdAt)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Keywords Section */}
      <div className="bg-zinc-900 rounded-[20px] p-6">
        <h3 className="text-white text-[20px] font-bold mb-4">Keywords</h3>
        <div className="flex flex-wrap gap-2">
          {allKeywords
            .slice(0, showAllKeywords ? undefined : 5)
            .map((keyword, index) => (
              <span
                key={index}
                onClick={() => onKeywordClick(keyword)}
                className="bg-zinc-800 hover:bg-orange-500 text-white/80 hover:text-white px-3 py-1 rounded-full text-sm cursor-pointer transition-colors"
              >
                {keyword.replace(/[\[\]"]/g, '')}
              </span>
            ))}
          {allKeywords.length > 5 && (
            <button
              onClick={() => setShowAllKeywords(!showAllKeywords)}
              className="text-orange-500 hover:text-orange-400 text-sm font-medium transition-colors"
            >
              {showAllKeywords ? 'Less' : 'More'}
            </button>
          )}
        </div>
      </div>

      {/* Any Questions Section */}
      <div className="bg-zinc-900 rounded-[20px] p-6">
        <h3 className="text-white text-[20px] font-bold mb-4">Any Questions?</h3>
        <p className="text-white/70 text-sm mb-4">
          Have questions about our services or need help with your project?
        </p>
        <Link
          href="/contact"
          className="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-4 rounded-lg transition-colors text-center block"
        >
          Let's Talk
        </Link>
      </div>
    </div>
  )
}
