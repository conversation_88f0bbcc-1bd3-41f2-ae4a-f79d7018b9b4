// BLOGS API
import axios from "axios";

// Base URL for backend API
const BASE_URL = "http://localhost:8001/api";

// Create axios instance
const apiClient = axios.create({
    baseURL: BASE_URL,
    timeout: 10000, // 10 seconds timeout
    headers: {
        'Content-Type': 'application/json',
    }
});

// Interface for blog data
export interface BlogData {
    _id: string;
    title: string;
    description: string;
    imageUrl: string;
    category: string[];
    keywords: string[];
    views: number;
    createdAt: string;
    updatedAt: string;
    id: number;
    __v: number;
}

// Interface for blog API response
export interface BlogApiResponse {
    status: boolean;
    code: number;
    message: string;
    data: {
        data: BlogData[];
        totalCount: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPreviousPage: boolean;
        currentPage: number;
        pageSize: number;
        filters: any;
    };
}

// Interface for single blog response
export interface SingleBlogApiResponse {
    status: boolean;
    code: number;
    message: string;
    data: BlogData;
}

// Get all blogs with optional filters
export const getAllBlogs = async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
}): Promise<BlogApiResponse> => {
    try {
        const queryParams = new URLSearchParams();
        
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.search) queryParams.append('search', params.search);
        if (params?.category) queryParams.append('category', params.category);

        const url = `/blogs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await apiClient.get(url);

        return response.data;
    } catch (error: any) {
        console.error('Get all blogs error:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch blogs');
    }
};

// Get blog by ID
export const getBlogById = async (id: string): Promise<SingleBlogApiResponse> => {
    try {
        const response = await apiClient.get(`/blogs/${id}`);
        return response.data;
    } catch (error: any) {
        console.error('Get blog by ID error:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch blog');
    }
};

// Get blog categories with counts
export const getBlogCategories = async (): Promise<any> => {
    try {
        const response = await apiClient.get('/blogs/categories');
        return response.data;
    } catch (error: any) {
        console.error('Get blog categories error:', error);
        // Return mock data if API fails
        return {
            status: true,
            data: [
                { name: 'Technology', count: 15 },
                { name: 'AI', count: 12 },
                { name: 'Web Development', count: 8 },
                { name: 'Mobile Apps', count: 6 },
                { name: 'Digital Marketing', count: 4 }
            ]
        };
    }
};

// Get recent blogs
export const getRecentBlogs = async (limit: number = 3): Promise<BlogApiResponse> => {
    try {
        const response = await apiClient.get(`/blogs?limit=${limit}&sort=createdAt&order=desc`);
        return response.data;
    } catch (error: any) {
        console.error('Get recent blogs error:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch recent blogs');
    }
};

// Get blogs sorted by views (most viewed first) for home page
export const getBlogsByViews = async (limit: number = 2): Promise<BlogApiResponse> => {
    try {
        const response = await apiClient.get(`/blogs?limit=${limit}&sort=views&order=desc`);
        return response.data;
    } catch (error: any) {
        console.error('Get blogs by views error:', error);
        // Fallback to recent blogs if views sorting fails
        return getRecentBlogs(limit);
    }
};

// Search blogs
export const searchBlogs = async (query: string, filters?: {
    category?: string;
    page?: number;
    limit?: number;
}): Promise<BlogApiResponse> => {
    try {
        const queryParams = new URLSearchParams();
        queryParams.append('search', query);
        
        if (filters?.category) queryParams.append('category', filters.category);
        if (filters?.page) queryParams.append('page', filters.page.toString());
        if (filters?.limit) queryParams.append('limit', filters.limit.toString());

        const response = await apiClient.get(`/blogs?${queryParams.toString()}`);
        return response.data;
    } catch (error: any) {
        console.error('Search blogs error:', error);
        throw new Error(error.response?.data?.message || 'Failed to search blogs');
    }
};

// Utility function to format date
export const formatBlogDate = (dateString: string): string => {
    try {
        let date: Date;

        // Handle the custom format "21/6/2025, 3:27:30 am"
        if (dateString.includes(',')) {
            const datePart = dateString.split(',')[0].trim();
            const [day, month, year] = datePart.split('/');
            date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        } else {
            // Handle standard ISO date format
            date = new Date(dateString);
        }

        if (isNaN(date.getTime())) {
            return dateString;
        }

        // Get weekday name
        const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const weekday = weekdays[date.getDay()];

        // Format as "Weekday, DD/MM/YYYY"
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();

        return `${weekday}, ${day}/${month}/${year}`;
    } catch (error) {
        return dateString;
    }
};

// Utility function to get social share URLs
export const getSocialShareUrls = (blogId: string, title: string, url: string) => {
    const encodedTitle = encodeURIComponent(title);
    const encodedUrl = encodeURIComponent(url);
    
    return {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        twitter: `https://twitter.com/intent/tweet?text=${encodedTitle}&url=${encodedUrl}`,
        linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
        instagram: `https://www.instagram.com/` // Instagram doesn't support direct sharing
    };
};

// Export default
export default {
    getAllBlogs,
    getBlogById,
    getBlogCategories,
    getRecentBlogs,
    getBlogsByViews,
    searchBlogs,
    formatBlogDate,
    getSocialShareUrls
};
