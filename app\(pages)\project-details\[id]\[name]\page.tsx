"use client"
import React, { useState, useEffect } from 'react'
import { notFound } from 'next/navigation'
import ProjectHero from '../../components/ProjectHero'
import ProjectContent from '../../components/ProjectContent'
import ProjectNavigation from '../../components/ProjectNavigation'
import ContactUsHome from '@/app/_components/common/contactusHome'
import { getProjectById, getAllProjects, Project, generateProjectUrl } from '@/api/projects/projects_api'
import ScrollAnimationWrapper from "@/components/animations/ScrollAnimationWrapper"

interface ProjectDetailsPageProps {
  params: {
    id: string
    name: string
  }
}

export default function ProjectDetailsPage({ params }: ProjectDetailsPageProps) {
  const { id, name } = params
  const [project, setProject] = useState<Project | null>(null)
  const [allProjects, setAllProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchProjectAndNavigation = async () => {
      try {
        setLoading(true)
        // Fetch current project and all projects for navigation
        const [projectData, allProjectsData] = await Promise.all([
          getProjectById(id),
          getAllProjects()
        ])
        setProject(projectData)
        setAllProjects(allProjectsData)
        setError(null)
      } catch (err: any) {
        setError(err.message || 'Failed to load project')
        console.error('Error loading project:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchProjectAndNavigation()
  }, [id])

  if (loading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-[24px] font-bold">Loading Project...</div>
        </div>
      </div>
    )
  }

  if (error || !project) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-[24px] font-bold text-red-500">
            {error || 'Project not found'}
          </div>
        </div>
      </div>
    )
  }

  // Get navigation projects (previous and next)
  const getNavigationProjects = () => {
    if (!project || allProjects.length === 0) {
      return { nextProject: null, previousProject: null }
    }

    const currentIndex = allProjects.findIndex(p => p._id === project._id)

    if (currentIndex === -1) {
      return { nextProject: null, previousProject: null }
    }

    const nextIndex = (currentIndex + 1) % allProjects.length // Loop to first if at end
    const previousIndex = currentIndex === 0 ? allProjects.length - 1 : currentIndex - 1 // Loop to last if at beginning

    const nextProject = allProjects[nextIndex]
    const previousProject = allProjects[previousIndex]

    return {
      nextProject: {
        id: nextProject._id,
        title: nextProject.title,
        slug: nextProject.title
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim()
      },
      previousProject: {
        id: previousProject._id,
        title: previousProject.title,
        slug: previousProject.title
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim()
      }
    }
  }

  const { nextProject, previousProject } = getNavigationProjects()

  // Transform API project data to match component expectations
  const projectDetails = {
    id: project._id,
    title: project.title,
    category: project.projectCategory,
    overview: {
      description: project.description,
      challenge: project.processAndChallengeDescription,
      solution: project.processAndChallengeDescription2
    },
    processSteps: [
      {
        step: "02",
        title: "Process & Challenge",
        description: project.processAndChallengeDescription,
        points: project.processAndChallengePoints,
        description2: project.processAndChallengeDescription2
      },
      {
        step: "03",
        title: "Summary",
        description: project.summaryDescription
      }
    ],
    images: {
      hero: project.bigImageUrl,
      gallery: project.miniImages
    },
    technologies: project.tags,
    features: project.processAndChallengePoints,
    projectLink: project.projectLink,
    projectDetails: {
      client: project.clientName || 'N/A',
      duration: project.projectDeliveryDate ? new Date(project.projectDeliveryDate).toLocaleDateString() : 'N/A',
      categories: project.projectCategory,
      website: project.projectLink
    }
  }

  return (
    <div className=" text-white">
      {/* Project Hero Section */}
      <ScrollAnimationWrapper animation="fade-in">
        <ProjectHero projectDetails={projectDetails} />
      </ScrollAnimationWrapper>

      {/* Project Content Section */}
      <ScrollAnimationWrapper animation="fade-in-up" delay={200}>
        <ProjectContent projectDetails={projectDetails} />
      </ScrollAnimationWrapper>

      {/* Project Navigation Section */}
      <ScrollAnimationWrapper animation="fade-in-left" delay={300}>
        <ProjectNavigation
          nextProject={nextProject}
          previousProject={previousProject}
        />
      </ScrollAnimationWrapper>

      {/* Contact section without animation as requested */}
      <ContactUsHome/>
    </div>
  )
}


