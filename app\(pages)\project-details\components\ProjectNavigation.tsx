"use client"
import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { BsArrowRight } from "react-icons/bs";
import { BsArrowLeft } from "react-icons/bs";

interface ProjectNavigationProps {
  nextProject: {
    id: string
    title: string
    slug: string
  } | null
  previousProject: {
    id: string
    title: string
    slug: string
  } | null
}

export default function ProjectNavigation({ nextProject, previousProject }: ProjectNavigationProps) {
  // Don't render if no projects available
  if (!nextProject && !previousProject) {
    return null
  }

  return (
    <div className="w-full text-white py-[25px] md:py-[30px] lg:py-[40px] px-[16px] md:px-[24px]">
      <div className="max-w-[95%] mx-auto px-10  pt-30 border-t border-white/10">

        {/* Navigation Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12">

          {/* Previous Project */}
          {previousProject && (
            <Link href={`/project-details/${previousProject.id}/${previousProject.slug}`}>
              <div className=" flex flex-row gap-6 rounded-[24px] p-6 md:p-8 transition-all duration-300 hover:scale-105 group cursor-pointer">
                <div className="w-[75px] h-[75px] text-[28px] border border-white rounded-full flex items-center justify-center group-hover:text-[#FF640F] group-hover:border-[#FF640F] transition-colors">

                  <BsArrowLeft />

                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-[14px] md:text-[16px] text-white/60 uppercase tracking-wider">
                      Previous
                    </span>
                    <h3 className="text-[20px] md:text-[24px] lg:text-[28px] font-bold text-white group-hover:text-[#FF640F] transition-colors">
                      {previousProject.title}
                    </h3>
                  </div>


                </div>
              </div>
            </Link>
          )}

          {/* Next Project */}
          {nextProject && (
            <Link href={`/project-details/${nextProject.id}/${nextProject.slug}`}>
              <div className=" flex flex-row gap-6 justify-end  rounded-[24px] p-6 md:p-8 transition-all duration-300 hover:scale-105 group cursor-pointer">


                <div className="flex items-center ">
                  <div className='flex flex-col gap-2  items-end'>
                    <span className="text-[14px] md:text-[16px] text-white/60 uppercase tracking-wider">
                      Next
                    </span>
                    <h3 className="text-[20px] md:text-[24px] lg:text-[28px] font-bold text-white group-hover:text-[#FF640F] transition-colors">
                      {nextProject.title}
                    </h3>
                  </div>


                </div>

                <div className="w-[75px] h-[75px] text-[28px]  border border-white rounded-full flex items-center justify-center group-hover:text-[#FF640F] group-hover:border-[#FF640F] transition-colors">
                  <BsArrowRight />

                </div>
              </div>
            </Link>
          )}
        </div>

        {/* Back to Projects */}
        {/* <div className="text-center mt-[48px] md:mt-[64px]">
          <Link href="/projects">
            <button className="border border-white text-white text-[18px] md:text-[20px] px-[32px] py-[16px] rounded-full font-medium hover:bg-white hover:text-black transition-colors">
              View All Projects
            </button>
          </Link>
        </div> */}
      </div>
    </div>
  )
}
