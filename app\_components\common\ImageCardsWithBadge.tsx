"use client";
import React from "react";
import Image, { StaticImageData } from "next/image";

interface ImageCardsWithBadgeProps {
  leftImage: StaticImageData;
  rightImage: StaticImageData;
  leftImageAlt: string;
  rightImageAlt: string;
  curveArrowImage: StaticImageData;
  badgeText?: string;
  badgeLetter?: string;
  // Simple controls for curved arrow
  arrowBottom?: string;
  arrowWidth?: string;
  arrowHeight?: string;
}

const ImageCardsWithBadge: React.FC<ImageCardsWithBadgeProps> = ({
  leftImage,
  rightImage,
  leftImageAlt,
  rightImageAlt,
  curveArrowImage,
  badgeText = "BEST AWARD WINNER",
  badgeLetter = "A",
  arrowBottom = "28rem",
  arrowWidth = "200px",
  arrowHeight = "220px"
}) => {
  return (
    <div className="relative mt-[48px] md:mt-[64px] lg:mt-[80px] xl:mt-[112px]">
      {/* Image Cards Container */}
      <div className="flex flex-col md:flex-row justify-between gap-[16px] md:gap-[24px] max-w-[100%]">
        {/* Left Image Card */}
        <div className="rounded-[16px] md:rounded-[24px] w-full md:w-[40%]">
          <Image
            src={leftImage}
            alt={leftImageAlt}
            className="object-cover rounded-[16px] md:rounded-[24px] w-[100%] h-[300px] md:h-[400px] lg:h-[500px] xl:h-[558px]"
          />
        </div>

        {/* Right Image Card */}
        <div className="rounded-[16px] md:rounded-[24px] w-full md:w-[60%]">
          <Image
            src={rightImage}
            alt={rightImageAlt}
            className="object-cover rounded-[16px] md:rounded-[24px] w-[100%] h-[300px] md:h-[400px] lg:h-[500px] xl:h-[558px]"
          />
        </div>
      </div>

      {/* Curved Arrow - Positioned between images */}
      <div
        className="absolute left-[33%] md:left-[30%] lg:left-[25.5%] xl:left-[30.5%] transform -translate-x-1/2 -translate-y-1/2 -rotate-45 z-50 hidden md:block"
        style={{ bottom: arrowBottom }}
      >
        <Image
          src={curveArrowImage}
          alt="Curved Arrow"
          style={{
            width: arrowWidth,
            height: arrowHeight
          }}
        />
      </div>

      {/* Circular Badge - Positioned at the bottom center between images */}
      <div className="absolute lg:bottom-[29rem] xl:bottom-[32rem] md:bottom-[25rem] bottom-[30rem] left-[40%] md:left-[41%] lg:left-[40.5%] xl:left-[40.5%] transform -translate-x-1/2 z-50">
        <div className="w-[96px] h-[96px] md:w-[112px] md:h-[112px] lg:w-[128px] lg:h-[128px] xl:w-[160px] xl:h-[160px] rounded-full bg-white flex items-center justify-center relative shadow-lg">
          {/* Center letter */}
          <span
            className="text-black font-bold z-10"
            style={{
              fontSize: typeof window !== 'undefined' ? (
                window.innerWidth >= 1600 ? '40px' :
                window.innerWidth >= 1024 ? '30px' :
                window.innerWidth >= 768 ? '30px' : '24px'
              ) : '30px'
            }}
          >
            {badgeLetter}
          </span>

          {/* Circular Text using SVG */}
          <svg viewBox="0 0 200 200" className="absolute w-full h-full p-1 md:p-2">
            <defs>
              <path
                id="circlePath"
                d="M100,100 m-85,0 a85,85 0 1,1 170,0 a85,85 0 1,1 -170,0"
              />
            </defs>
            <text
              fill="black"
              fontSize="14"
              letterSpacing="18"
              fontFamily="sans-serif"
              className="text-xs md:text-sm"
            >
              <textPath href="#circlePath">
                {badgeText}
              </textPath>
            </text>
          </svg>
        </div>
      </div>
    </div>
  );
};

export default ImageCardsWithBadge;
