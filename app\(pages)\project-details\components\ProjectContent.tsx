"use client"
import React from 'react'
import Image from 'next/image'
import { IoIosCheckmark } from "react-icons/io"

interface ProjectContentProps {
  projectDetails: any
}

export default function ProjectContent({ projectDetails }: ProjectContentProps) {
  // Find Process & Challenge step (step 02)
  const processStep = projectDetails.processSteps.find((step: any) => step.step === "02")
  // Find Summary step (step 03)
  const summaryStep = projectDetails.processSteps.find((step: any) => step.step === "03")

  return (
    <div className="w-full  text-white py-[32px] md:py-[48px] lg:py-[64px] px-[16px] md:px-[24px]">
      <div className="max-w-[95%] mx-auto px-10">

        {/* Process & Challenge Section */}
        {processStep && (
          <div className="mb-[48px] md:mb-[64px] lg:mb-[80px]">
            <div className="text-[48px] md:text-[45px] lg:text-[45px] font-bold leading-[70px] tracking-[-1px] flex flex-row items-start justify-start gap-8">
              <span className="w-[10%] text-[48px] md:text-[48px] lg:text-[48px] font-bold text-white">
                {processStep.step}
              </span>
              <div className='w-[90%] flex flex-col justify-between gap-6 items-start'>
                <span className='text-[48px] md:text-[45px] lg:text-[45px] font-bold leading-[70px] tracking-[-1px]'>
                  {processStep.title}
                </span>

                <div className='flex flex-col gap-8'>
                  <div
                    className="text-[18px] md:text-[20px] lg:text-[22px] text-white/70 leading-[42px] prose prose-invert max-w-none [&>p]:mb-4 [&>a]:text-blue-400 [&>a]:underline [&>strong]:text-white [&>em]:text-white/80"
                    dangerouslySetInnerHTML={{ __html: processStep.description }}
                  />

                  {processStep.points && (
                    <ul className="space-y-4 ">
                      {processStep.points.map((point: string, pointIndex: number) => (
                        <li key={pointIndex} className="flex items-center  gap-3">
                          <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center ">
                            <IoIosCheckmark className="w-6 h-6 text-[20px] text-black" />
                          </div>
                          <span className="text-[22px] md:text-[22px] leading-[55px] text-white">
                            {point}
                          </span>
                        </li>
                      ))}
                    </ul>
                  )}

                   <div
                    className="text-[18px] md:text-[20px] lg:text-[22px] text-white/70 leading-[42px] prose prose-invert max-w-none [&>p]:mb-4 [&>a]:text-blue-400 [&>a]:underline [&>strong]:text-white [&>em]:text-white/80"
                    dangerouslySetInnerHTML={{ __html: processStep.description2 }}
                  />
                </div>

                {/* Three Images Grid */}
                <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                  {projectDetails.images.gallery.slice(0, 3).map((img: any, imgIndex: number) => (
                    <div
                      key={imgIndex}
                      className="relative h-[200px] md:h-[250px] rounded-[24px] overflow-hidden bg-[#1a1a1a]"
                    >
                      <Image
                        src={img}
                        alt={`Process image ${imgIndex + 1}`}
                        width={400}
                        height={250}
                        className="object-cover w-full h-full"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Summary Section */}
        {summaryStep && (
          <div className="mb-[38px] md:mb-[54px] lg:mb-[50px]">
            <div className="text-[48px] md:text-[45px] lg:text-[45px] font-bold leading-[70px] tracking-[-1px] flex flex-row items-start justify-start gap-8">
              <span className="w-[10%] text-[48px] md:text-[48px] lg:text-[48px] font-bold text-white">
                {summaryStep.step}
              </span>
              <div className='w-[90%] flex flex-col justify-between gap-6 items-start'>
                <span className='text-[48px] md:text-[45px] lg:text-[45px] font-bold leading-[70px] tracking-[-1px]'>
                  {summaryStep.title}
                </span>

                <div>
                  <div
                    className="text-[18px] md:text-[20px] lg:text-[22px] text-white/70  leading-[40px] prose prose-invert max-w-none [&>p]:mb-4 [&>a]:text-blue-400 [&>a]:underline [&>strong]:text-white [&>em]:text-white/80"
                    dangerouslySetInnerHTML={{ __html: summaryStep.description }}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
