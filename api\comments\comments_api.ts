import axios from "axios";

// Base URL configuration
const BASE_URL = "http://localhost:8001/api";

// Create axios instance with base configuration
const apiClient = axios.create({
    baseURL: BASE_URL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    }
});

// Comment interface based on API response
export interface Comment {
    id: number;
    name: string;
    email: string;
    comment: string;
    status: 'pending' | 'approved' | 'rejected' | 'spam';
    isVisible: boolean;
    moderatedBy?: string;
    moderatedAt?: string;
    ipAddress: string;
    userAgent: string;
    createdAt: string;
    updatedAt: string;
}

// API Response interface
export interface CommentApiResponse {
    status: boolean;
    code: number;
    message: string;
    data: Comment[];
    pagination?: {
        currentPage: number;
        totalPages: number;
        totalItems: number;
        itemsPerPage: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
    };
}

// Single Comment Response interface
export interface SingleCommentResponse {
    status: boolean;
    code: number;
    message: string;
    data: Comment;
}

// Comment Statistics interface
export interface CommentStatistics {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    spam: number;
    visible: number;
}

// Create Comment interface (Public)
export interface CreateCommentData {
    name: string;
    email: string;
    comment: string;
    blogId: string;
}

// Update Comment interface (Admin)
export interface UpdateCommentData {
    status?: 'pending' | 'approved' | 'rejected' | 'spam';
    isVisible?: boolean;
    moderatedBy?: string;
}

// Submit new comment (Public endpoint)
export const submitComment = async (commentData: CreateCommentData): Promise<SingleCommentResponse> => {
    try {
        const response = await apiClient.post('/comments', commentData);
        return response.data;
    } catch (error) {
        console.error('Error submitting comment:', error);
        throw error;
    }
};

// Get approved comments (Public endpoint)
export const getApprovedComments = async (params?: {
    page?: number;
    limit?: number;
    blogId?: string;
}): Promise<CommentApiResponse> => {
    try {
        const queryParams = new URLSearchParams();

        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.blogId) queryParams.append('blogId', params.blogId);

        const url = `/comments/approved${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await apiClient.get(url);

        return response.data;
    } catch (error) {
        console.error('Error fetching approved comments:', error);
        throw error;
    }
};

// Get all comments with optional filters (Admin endpoint)
export const getAllComments = async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    isVisible?: boolean;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}): Promise<CommentApiResponse> => {
    try {
        const queryParams = new URLSearchParams();
        
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.status) queryParams.append('status', params.status);
        if (params?.isVisible !== undefined) queryParams.append('isVisible', params.isVisible.toString());
        if (params?.search) queryParams.append('search', params.search);
        if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

        const url = `/comments${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await apiClient.get(url);
        
        return response.data;
    } catch (error) {
        console.error('Error fetching all comments:', error);
        throw error;
    }
};

// Get comment by ID (Admin endpoint)
export const getCommentById = async (id: number): Promise<SingleCommentResponse> => {
    try {
        const response = await apiClient.get(`/comments/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching comment by ID:', error);
        throw error;
    }
};

// Update comment (Admin endpoint - for moderation)
export const updateComment = async (id: number, commentData: UpdateCommentData): Promise<SingleCommentResponse> => {
    try {
        const response = await apiClient.put(`/comments/${id}`, commentData);
        return response.data;
    } catch (error) {
        console.error('Error updating comment:', error);
        throw error;
    }
};

// Delete comment by ID (Admin endpoint)
export const deleteComment = async (id: number): Promise<{ status: boolean; message: string }> => {
    try {
        const response = await apiClient.delete(`/comments/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error deleting comment:', error);
        throw error;
    }
};

// Get comment statistics (Admin endpoint)
export const getCommentStatistics = async (): Promise<{ status: boolean; data: CommentStatistics }> => {
    try {
        const response = await apiClient.get('/comments/statistics');
        return response.data;
    } catch (error) {
        console.error('Error fetching comment statistics:', error);
        throw error;
    }
};

// Approve comment (Admin helper function)
export const approveComment = async (id: number, moderatedBy: string): Promise<SingleCommentResponse> => {
    try {
        return await updateComment(id, {
            status: 'approved',
            isVisible: true,
            moderatedBy
        });
    } catch (error) {
        console.error('Error approving comment:', error);
        throw error;
    }
};

// Reject comment (Admin helper function)
export const rejectComment = async (id: number, moderatedBy: string): Promise<SingleCommentResponse> => {
    try {
        return await updateComment(id, {
            status: 'rejected',
            isVisible: false,
            moderatedBy
        });
    } catch (error) {
        console.error('Error rejecting comment:', error);
        throw error;
    }
};

// Mark comment as spam (Admin helper function)
export const markCommentAsSpam = async (id: number, moderatedBy: string): Promise<SingleCommentResponse> => {
    try {
        return await updateComment(id, {
            status: 'spam',
            isVisible: false,
            moderatedBy
        });
    } catch (error) {
        console.error('Error marking comment as spam:', error);
        throw error;
    }
};

// Utility function to format comment date
export const formatCommentDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// Utility function to get comment status color
export const getCommentStatusColor = (status: string): string => {
    switch (status) {
        case 'approved': return 'text-green-500';
        case 'rejected': return 'text-red-500';
        case 'spam': return 'text-orange-500';
        case 'pending': return 'text-yellow-500';
        default: return 'text-gray-500';
    }
};

// Export default
export default {
    submitComment,
    getApprovedComments,
    getAllComments,
    getCommentById,
    updateComment,
    deleteComment,
    getCommentStatistics,
    approveComment,
    rejectComment,
    markCommentAsSpam,
    formatCommentDate,
    getCommentStatusColor
};
