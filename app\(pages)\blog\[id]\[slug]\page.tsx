"use client"
import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useParams, useRouter } from 'next/navigation'
import { BsArrowLeft } from "react-icons/bs"
import { FaFacebook, FaInstagram, FaLinkedin } from "react-icons/fa"
import { getBlogById, getBlogCategories, getRecentBlogs, formatBlogDate, getSocialShareUrls, BlogData } from '@/api/blogs/blogs_api'
import BlogSidebar from '@/app/(pages)/blog/componets/BlogSidebar'
import BlogComments from '@/app/(pages)/blog/componets/BlogComments'

export default function BlogDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [blog, setBlog] = useState<BlogData | null>(null)
  const [categories, setCategories] = useState<Array<{ name: string; count: number }>>([])
  const [recentBlogs, setRecentBlogs] = useState<BlogData[]>([])
  const [loading, setLoading] = useState(true)
  const [showAllKeywords, setShowAllKeywords] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    if (params.id) {
      fetchBlogDetail(params.id as string)
      fetchCategories()
      fetchRecentBlogs()
    }
  }, [params.id])

  const fetchBlogDetail = async (id: string) => {
    try {
      setLoading(true)
      const response = await getBlogById(id)
      if (response.status) {
        setBlog(response.data)
      }
    } catch (error) {
      console.error('Error fetching blog detail:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await getBlogCategories()
      if (response.status) {
        setCategories(response.data)
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  const fetchRecentBlogs = async () => {
    try {
      const response = await getRecentBlogs(3)
      if (response.status) {
        setRecentBlogs(response.data.data)
      }
    } catch (error) {
      console.error('Error fetching recent blogs:', error)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/blog?search=${encodeURIComponent(searchQuery)}`)
    }
  }

  const handleSearchChange = (query: string) => {
    setSearchQuery(query)
  }

  const handleCategoryFilter = (category: string) => {
    router.push(`/blog?category=${encodeURIComponent(category)}`)
  }

  const handleKeywordClick = (keyword: string) => {
    router.push(`/blog?search=${encodeURIComponent(keyword)}`)
  }

  const handleBlogClick = (blogId: string, title: string) => {
    const slug = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
    router.push(`/blog/${blogId}/${slug}`)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-white text-xl">Loading blog...</div>
        </div>
      </div>
    )
  }

  if (!blog) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-white text-xl mb-4">Blog not found</div>
          <Link href="/blog" className="text-orange-500 hover:text-orange-400">
            Back to Blogs
          </Link>
        </div>
      </div>
    )
  }

  const shareUrls = getSocialShareUrls(blog._id, blog.title, window.location.href)

  return (
    <div className=" bg-black text-white py-20">
      <div className="w-[95%] max-w-[1400px] mx-auto flex gap-8">
        {/* Left Side - Blog Content */}
        <div className="flex-1">
          {/* Back Button */}
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 text-white/70 hover:text-orange-500 mb-6 transition-colors"
          >
            <BsArrowLeft className="w-5 h-5" />
            <span>Back to Blogs</span>
          </button>

          {/* Blog Header */}
          <div className="mb-8">
            {/* Categories */}
            <div className="flex flex-wrap gap-2 mb-4">
              {blog.category.map((cat, index) => (
                <span key={index} className="bg-orange-500 text-white px-3 py-1 rounded-full text-sm">
                  {cat}
                </span>
              ))}
            </div>

            {/* Title */}
            <h1 className="text-4xl md:text-5xl font-bold text-white leading-tight mb-4">
              {blog.title}
            </h1>

            {/* Meta Info */}
            <div className="flex items-center gap-4 text-white/70 mb-6">
              <span>Published: {formatBlogDate(blog.createdAt)}</span>
              <span>•</span>
              <span>Views: {blog.views}</span>
            </div>

            {/* Featured Image */}
            <div className="relative w-full h-[400px] rounded-[20px] overflow-hidden mb-8">
              <Image
                src={blog.imageUrl}
                alt={blog.title}
                width={800}
                height={400}
                className="object-cover w-full h-full"
              />
            </div>
          </div>

          {/* Blog Content */}
          <div className="prose prose-invert max-w-none mb-8">
            <div
              className="text-white/90 text-lg leading-relaxed blog-content"
              dangerouslySetInnerHTML={{ __html: blog.description }}
            />
          </div>

          {/* Keywords Section */}
          <div className="bg-zinc-900 rounded-[20px] p-6 mb-8">
            <h3 className="text-white text-xl font-bold mb-4">Keywords</h3>
            <div className="flex flex-wrap gap-2">
              {blog.keywords
                .slice(0, showAllKeywords ? undefined : 5)
                .map((keyword, index) => (
                  <span
                    key={index}
                    className="bg-zinc-800 text-white/80 px-3 py-1 rounded-full text-sm"
                  >
                    {keyword.replace(/[\[\]"]/g, '')}
                  </span>
                ))}
              {blog.keywords.length > 5 && (
                <button
                  onClick={() => setShowAllKeywords(!showAllKeywords)}
                  className="text-orange-500 hover:text-orange-400 text-sm font-medium transition-colors"
                >
                  {showAllKeywords ? 'Show Less' : 'More'}
                </button>
              )}
            </div>
          </div>

          {/* Social Share */}
          <div className="bg-zinc-900 rounded-[20px] p-6">
            <h3 className="text-white text-xl font-bold mb-4">Share this article</h3>
            <div className="flex gap-4">
              <button
                onClick={() => window.open(shareUrls.facebook, '_blank')}
                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <FaFacebook className="w-5 h-5" />
                <span>Facebook</span>
              </button>
              <button
                onClick={() => window.open(shareUrls.linkedin, '_blank')}
                className="flex items-center gap-2 bg-blue-700 hover:bg-blue-800 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <FaLinkedin className="w-5 h-5" />
                <span>LinkedIn</span>
              </button>
              <button
                onClick={() => window.open(shareUrls.instagram, '_blank')}
                className="flex items-center gap-2 bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <FaInstagram className="w-5 h-5" />
                <span>Instagram</span>
              </button>
            </div>
          </div>
          {/* Blog Comments Section */}
      <BlogComments
        blogId={Array.isArray(params.id) ? params.id[0] : params.id}
        blogTitle={blog?.title}
      />
        </div>

        {/* Right Sidebar */}
        <BlogSidebar
          categories={categories}
          recentBlogs={recentBlogs}
          allKeywords={blog?.keywords || []}
          searchQuery={searchQuery}
          selectedCategory=""
          onSearchChange={handleSearchChange}
          onSearchSubmit={handleSearch}
          onCategoryFilter={handleCategoryFilter}
          onKeywordClick={handleKeywordClick}
        />
      </div>

      
    </div>
  )
}
