"use client"
import React from 'react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { BsArrowRight } from "react-icons/bs"
import { FaFacebook, FaInstagram, FaLinkedin } from "react-icons/fa"
import { formatBlogDate, getSocialShareUrls, BlogData } from '@/api/blogs/blogs_api'

interface BlogCardProps {
  blog: BlogData
}

export default function BlogCard({ blog }: BlogCardProps) {
  const router = useRouter()

  const handleBlogClick = () => {
    const slug = blog.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
    router.push(`/blog/${blog._id}/${slug}`)
  }

  const handleSocialShare = (e: React.MouseEvent, platform: 'facebook' | 'instagram' | 'linkedin') => {
    e.stopPropagation()
    const shareUrls = getSocialShareUrls(blog._id, blog.title, window.location.href)
    
    switch (platform) {
      case 'facebook':
        window.open(shareUrls.facebook, '_blank')
        break
      case 'instagram':
        window.open(shareUrls.instagram, '_blank')
        break
      case 'linkedin':
        window.open(shareUrls.linkedin, '_blank')
        break
    }
  }

  return (
    <div
      onClick={handleBlogClick}
      className="bg-zinc-900 rounded-[20px] cursor-pointer p-0 flex flex-col relative overflow-hidden transition-all duration-300 group hover:scale-105"
    >
      {/* Blog Image */}
      <div className="relative w-full h-[300px] rounded-t-[20px] overflow-hidden">
        <Image
          src={blog.imageUrl}
          alt={blog.title}
          width={500}
          height={300}
          className="object-cover w-full h-full"
        />
        {/* Date - Top Left */}
        <div className="absolute top-4 left-4 bg-black/70 backdrop-blur-sm rounded-lg px-3 py-1">
          <span className="text-white text-sm font-medium">
            {formatBlogDate(blog.createdAt)}
          </span>
        </div>
        {/* Arrow Icon - Top Right */}
        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="bg-white/20 backdrop-blur-sm rounded-full p-2">
            <BsArrowRight className="w-5 h-5 text-white" />
          </div>
        </div>
      </div>

      {/* Blog Content */}
      <div className="p-6 flex flex-col gap-4 flex-1">
        {/* Category with Hover Dropdown */}
        <div className="relative group/category">
          <div className="flex items-center gap-2">
            <span className="bg-orange-500 text-white px-3 py-1 rounded-full text-sm">
              {blog.category[0]}
            </span>
            {blog.category.length > 1 && (
              <span className="text-white/50 text-sm cursor-pointer">
                +{blog.category.length - 1} more
              </span>
            )}
          </div>
          {/* Dropdown for additional categories */}
          {blog.category.length > 1 && (
            <div className="absolute top-full left-0 mt-2 bg-zinc-800 rounded-lg p-3 opacity-0 group-hover/category:opacity-100 transition-opacity duration-300 z-10 min-w-[200px]">
              <div className="flex flex-wrap gap-2">
                {blog.category.slice(1).map((cat, index) => (
                  <span key={index} className="bg-orange-500/80 text-white px-2 py-1 rounded-full text-xs">
                    {cat}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Title */}
        <h3 className="text-[20px] font-bold text-white leading-tight line-clamp-2">
          {blog.title}
        </h3>

        {/* Description */}
        <p className="text-white/70 text-sm line-clamp-3">
          {blog.description}
        </p>

        {/* Bottom Section */}
        <div className="flex justify-between items-end mt-auto pt-4">
          {/* Keywords - Only show first 2 */}
          <div className="flex flex-wrap gap-2">
            {blog.keywords.slice(0, 2).map((keyword, index) => (
              <span key={index} className="bg-zinc-700 text-white/80 px-2 py-1 rounded text-xs">
                {keyword.replace(/[\[\]"]/g, '')}
              </span>
            ))}
          </div>

          {/* Social Share Icons */}
          <div className="flex gap-2">
            <button
              onClick={(e) => handleSocialShare(e, 'facebook')}
              className="p-2 bg-zinc-700 hover:bg-blue-600 rounded-full transition-colors"
            >
              <FaFacebook className="w-4 h-4 text-white" />
            </button>
            <button
              onClick={(e) => handleSocialShare(e, 'instagram')}
              className="p-2 bg-zinc-700 hover:bg-pink-600 rounded-full transition-colors"
            >
              <FaInstagram className="w-4 h-4 text-white" />
            </button>
            <button
              onClick={(e) => handleSocialShare(e, 'linkedin')}
              className="p-2 bg-zinc-700 hover:bg-blue-700 rounded-full transition-colors"
            >
              <FaLinkedin className="w-4 h-4 text-white" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
