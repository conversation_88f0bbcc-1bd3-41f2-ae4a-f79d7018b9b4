"use client";

import React, { useState, useEffect } from 'react';
import { FiHelpCircle, FiChevronDown, FiChevronUp, FiSearch } from 'react-icons/fi';
import { getAllFAQs, getFAQCategories, FAQ } from '@/api/faqs/faqs_api';
import ContactUsHome from '@/app/_components/common/contactusHome';
import ScrollAnimationWrapper from "@/components/animations/ScrollAnimationWrapper";

const FAQsPage: React.FC = () => {
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [filteredFaqs, setFilteredFaqs] = useState<FAQ[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [openFaqId, setOpenFaqId] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchFAQsAndCategories();
  }, []);

  useEffect(() => {
    filterFAQs();
  }, [faqs, selectedCategory, searchQuery]);

  const fetchFAQsAndCategories = async () => {
    try {
      setLoading(true);
      const [faqsResponse, categoriesResponse] = await Promise.all([
        getAllFAQs({ status: 'active', limit: 1000, sortBy: 'order', sortOrder: 'asc' }),
        getFAQCategories()
      ]);

      setFaqs(faqsResponse.data);
      setCategories(['All', ...categoriesResponse.data]);
    } catch (err) {
      console.error('Error fetching FAQs:', err);
      setError('Failed to load FAQs. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const filterFAQs = () => {
    let filtered = faqs;

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(faq => faq.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(faq => 
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query)
      );
    }

    setFilteredFaqs(filtered);
  };

  const toggleFaq = (faqId: number) => {
    setOpenFaqId(openFaqId === faqId ? null : faqId);
  };

  const getCategoryCount = (category: string) => {
    if (category === 'All') return faqs.length;
    return faqs.filter(faq => faq.category === category).length;
  };

  if (loading) {
    return (
      <div className=" flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className=" flex items-center justify-center">
        <div className="text-center text-white">
          <FiHelpCircle className="mx-auto h-16 w-16 text-red-500 mb-4" />
          <h2 className="text-2xl font-bold mb-2">Error Loading FAQs</h2>
          <p className="text-gray-400">{error}</p>
          <button 
            onClick={fetchFAQsAndCategories}
            className="mt-4 px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full text-white py-12">
      <div className="w-[80%] mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <ScrollAnimationWrapper animation="fade-in-up">
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-500 rounded-full mb-6">
              <FiHelpCircle className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white mb-4">
              Frequently Asked Questions
            </h1>

          </div>
        </ScrollAnimationWrapper>

        {/* Search Bar */}
        {/* <div className="max-w-2xl mx-auto mb-8">
          <div className="relative">
            <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search FAQs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-4 bg-gray-900 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
          </div>
        </div> */}

        <ScrollAnimationWrapper animation="fade-in-left" delay={200}>
          <div className=" flex flex-col lg:flex-row gap-8">
            {/* Left Sidebar - Categories */}
            <div className="lg:w-1/4">
            <div className="bg-[#18181B] rounded-lg p-6  sticky top-8">
              <h3 className="text-lg font-semibold text-white mb-4">Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`w-full text-left px-4 py-3 rounded-lg transition-colors flex items-center justify-between ${
                      selectedCategory === category
                        ? 'bg-orange-600 text-white'
                        : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                    }`}
                  >
                    <span>{category}</span>
                    <span className={`text-sm px-2 py-1 rounded-full ${
                      selectedCategory === category
                        ? 'bg-orange-700 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}>
                      {getCategoryCount(category)}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Right Content - FAQs */}
          <div className="lg:w-3/4">
            <div className="mb-6">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-white">
                  {selectedCategory === 'All' ? 'All FAQs' : selectedCategory}
                </h2>
                <span className="text-sm text-gray-400">
                  {filteredFaqs.length} question{filteredFaqs.length !== 1 ? 's' : ''}
                </span>
              </div>
              {searchQuery && (
                <p className="text-sm text-gray-400 mt-2">
                  Showing results for "{searchQuery}"
                </p>
              )}
            </div>

            {filteredFaqs.length === 0 ? (
              <div className="text-center py-12">
                <FiHelpCircle className="mx-auto h-16 w-16 text-gray-600 mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">No FAQs Found</h3>
                <p className="text-gray-400">
                  {searchQuery 
                    ? `No FAQs match your search "${searchQuery}"`
                    : `No FAQs available in the ${selectedCategory} category`
                  }
                </p>
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="mt-4 text-orange-500 hover:text-orange-400 transition-colors"
                  >
                    Clear search
                  </button>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredFaqs.map((faq) => (
                  <div
                    key={faq.id}
                    className="bg-[#18181B] rounded-lg  overflow-hidden"
                  >
                    <button
                      onClick={() => toggleFaq(faq.id)}
                      className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-800/50 transition-colors"
                    >
                      <h3 className="text-lg font-medium text-white pr-4">
                        {faq.question}
                      </h3>
                      {openFaqId === faq.id ? (
                        <FiChevronUp className="w-5 h-5 text-orange-500 flex-shrink-0" />
                      ) : (
                        <FiChevronDown className="w-5 h-5 text-gray-400 flex-shrink-0" />
                      )}
                    </button>
                    
                    {openFaqId === faq.id && (
                      <div className="px-6 pb-4">
                        <div className="border-t border-gray-700 pt-4">
                          <p className="text-gray-300 leading-relaxed whitespace-pre-wrap">
                            {faq.answer}
                          </p>
                          {faq.category && (
                            <div className="mt-4">
                              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                {faq.category}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
          </div>
        </ScrollAnimationWrapper>

        {/* Contact section without animation as requested */}
        <ContactUsHome/>
      </div>
    </div>
  );
};

export default FAQsPage;
