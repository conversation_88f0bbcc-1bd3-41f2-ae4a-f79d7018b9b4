"use client"
import React from 'react'

interface BlogPaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
}

export default function BlogPagination({ currentPage, totalPages, onPageChange }: BlogPaginationProps) {
  if (totalPages <= 1) return null

  return (
    <div className="flex justify-center items-center gap-4 mt-8">
      {/* Previous Button */}
      {currentPage > 1 && (
        <button
          onClick={() => onPageChange(currentPage - 1)}
          className="px-4 py-2 rounded-lg transition-all duration-300 bg-zinc-800 text-white hover:bg-zinc-700"
        >
          Previous
        </button>
      )}

      {/* Page Numbers */}
      {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNumber) => (
        <button
          key={pageNumber}
          onClick={() => onPageChange(pageNumber)}
          className={`w-12 h-12 rounded-lg transition-all duration-300 ${
            currentPage === pageNumber
              ? 'bg-orange-500 text-white font-bold'
              : 'bg-zinc-800 text-white hover:bg-zinc-700'
          }`}
        >
          {pageNumber}
        </button>
      ))}

      {/* Next Button */}
      {currentPage < totalPages && (
        <button
          onClick={() => onPageChange(currentPage + 1)}
          className="px-4 py-2 rounded-lg transition-all duration-300 bg-zinc-800 text-white hover:bg-zinc-700"
        >
          Next
        </button>
      )}
    </div>
  )
}
