

import React from 'react';
import AboutIdentity from './identity';
import ImageCardsWithBadge from '@/app/_components/common/ImageCardsWithBadge';
import meeting from "@/public/new assests/news icons/heroicons/homeimage/meeting.png";
import tablemeet from "@/public/new assests/news icons/heroicons/homeimage/tablemeet.png";
import curvearrow from "@/public/new assests/news icons/heroicons/homeimage/curvearrow.svg";
import StatsComponent from './components/statescount';
import ContactUsHome from '@/app/_components/common/contactusHome';
import AboutValue from './aboutvalue';
import AboutOwners from './about-us';
import ScrollAnimationWrapper from "@/components/animations/ScrollAnimationWrapper";


const page = () => {
  return (
    <div >
     <div className='flex flex-col gap-10'>
       <ScrollAnimationWrapper animation="fade-in-up">
         <AboutIdentity/>
       </ScrollAnimationWrapper>

       <ScrollAnimationWrapper animation="scale-in" delay={200}>
         <ImageCardsWithBadge
           leftImage={tablemeet}
           rightImage={meeting}
           leftImageAlt="Team Meeting at Table"
           rightImageAlt="Business Meeting"
           curveArrowImage={curvearrow}
           badgeText="Best Award WINNER"
           badgeLetter="A"
           // Simple controls for curved arrow
           arrowBottom="30rem"    // Bottom position control
           arrowWidth="200px"     // Single width control
           arrowHeight="230px"    // Single height control
         />
       </ScrollAnimationWrapper>

       <ScrollAnimationWrapper animation="fade-in-up" delay={300}>
         <StatsComponent/>
       </ScrollAnimationWrapper>

       <ScrollAnimationWrapper animation="fade-in-left" delay={200}>
         <AboutValue/>
       </ScrollAnimationWrapper>

       <ScrollAnimationWrapper animation="fade-in-right" delay={300}>
         <AboutOwners/>
       </ScrollAnimationWrapper>

       {/* Contact section without animation as requested */}
       <ContactUsHome/>
     </div>
    </div>
  )
}

export default page
