"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Star } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import Image from "next/image";
import doublemark from "@/public/new assests/news icons/heroicons/doubleaxlimationmark.svg"

// Review data matching your Figma design
const reviews = [
  {
    id: 1,
    name: "MD Rashed Kabir",
    location: "California, USA",
    rating: 5,
    review: "Highly recommend this reliable SaaS provider for seamless workflow optimization.",
    profileIcon: doublemark,
  },
  {
    id: 2,
    name: "MD <PERSON> Kabir", 
    location: "California, USA",
    rating: 5,
    review: "Highly recommend this reliable SaaS provider for seamless workflow optimization.",
    profileIcon: doublemark
  },
  {
    id: 3,
    name: "MD Rashed Kabir",
    location: "California, USA", 
    rating: 5,
    review: "Highly recommend this reliable SaaS provider for seamless workflow optimization.",
    profileIcon: doublemark
  },
    {
    id: 4,
    name: "<PERSON>",
    location: "California, USA", 
    rating: 5,
    review: "Highly recommend this reliable SaaS provider for seamless workflow optimization.",
    profileIcon: doublemark
  },
    {
    id: 5,
    name: "MD <PERSON>",
    location: "California, USA", 
    rating: 5,
    review: "Highly recommend this reliable SaaS provider for seamless workflow optimization.",
    profileIcon: doublemark
  }
];

export default function FirstReviewCarousel() {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const router = useRouter();

  const handleReviewClick = () => {
    router.push('/allreviewclient.tsx');
  };

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  return (
    <section className="py-8">
      <div className="max-w-full ">
        {/* Title */}
        <div className="text-center mb-12">
          <h2 className="text-[65px] md:text-[70px] lg:text-[72px] font-bold text-white leading-[72px] tracking-[2px]">
            We've been helping Our<br />
            clients globally
          </h2>
        </div>

        {/* Carousel */}
        <div className="relative w-full">
          <Carousel
            setApi={setApi}
            opts={{
              align: "center",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent className="flex items-center">
              {reviews.map((review) => (
                <CarouselItem key={review.id} className="basis-[45%] flex justify-center">
                  <div 
                    className="bg-[#2A2A2A] rounded-2xl p-10 py-12 cursor-pointer hover:bg-[#333333] transition-colors duration-300 flex flex-col justify-start items-start gap-7"
                    onClick={handleReviewClick}
                  >
                    {/* Profile Icon */}
                    <div className="flex justify-center items-center  w-[62px] h-[62px] bg-gradient-to-t from-[#ffac6c] to-[#fa0096] rounded-full">
                    
                        <Image src={review.profileIcon} alt={review.name}  className="w-[19.53px] h-[21.53px]" />
                     
                    </div>

                    {/* Review Text */}
                    <div className="mb-6">
                      <p className="text-white text-[36px] leading-[53px] italic text-left">
                        "{review.review}"
                      </p>
                    </div>

                    {/* Name and Location */}
                   <div className="flex flex-row items-start justify-between w-full ">
                     <div className="text-center mb-4">
                      <h4 className="text-white font-semibold text-[24px] leading-[36px] ">
                        {review.name}
                      </h4>
                      <p className="text-white/40 text-[20px] leading-[36px] text-left">
                        {review.location}
                      </p>
                    </div>

                    {/* Star Rating */}
                    <div className="flex justify-center items-center gap-1">
                      {[...Array(review.rating)].map((_, i) => (
                        <Star
                          key={i}
                          className="w-5 h-5 fill-yellow-400 text-yellow-400"
                        />
                      ))}
                    </div>
                   </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>

          
        </div>
      </div>
    </section>
  );
}
